{"name": "Langflow Dev Container", "build": {"context": "..", "dockerfile": "Dockerfile"}, "features": {"ghcr.io/devcontainers/features/node": {}, "ghcr.io/dhoeric/features/hadolint:1": {}}, "postCreateCommand": "sudo chown -R langflow .venv .mypy_cache src/frontend/node_modules src/frontend/build src/backend/base/langflow/frontend && make install_frontend && make install_backend", "customizations": {"vscode": {"extensions": ["charliermarsh.ruff", "njpwerner.autodocstring", "oderwat.indent-rainbow", "ex<PERSON><PERSON>.had<PERSON>t", "actboy168.tasks", "GitHub.copilot", "ms-python.python", "eamodio.gitlens", "ms-vscode.makefile-tools", "GitHub.vscode-pull-request-github"], "settings": {"terminal.integrated.defaultProfile.linux": "zsh", "terminal.integrated.profiles.linux": {"zsh": {"path": "/bin/zsh"}}}}}, "remoteUser": "langflow", "workspaceMount": "source=${localWorkspaceFolder},target=/workspace,type=bind", "workspaceFolder": "/workspace", "forwardPorts": [7860, 3000], "containerEnv": {"FRONTEND_START_FLAGS": "--host"}, "mounts": [{"source": "${localWorkspaceFolderBasename}-frontend-node_modules", "target": "/workspace/src/frontend/node_modules", "type": "volume"}, {"source": "${localWorkspaceFolderBasename}-frontend-build", "target": "/workspace/src/frontend/build", "type": "volume"}, {"source": "${localWorkspaceFolderBasename}-backend-frontend", "target": "/workspace/src/backend/base/langflow/frontend", "type": "volume"}, {"source": "${localWorkspaceFolderBasename}-venv", "target": "/workspace/.venv", "type": "volume"}, {"source": "${localWorkspaceFolderBasename}-mypy-cache", "target": "/workspace/.mypy_cache", "type": "volume"}, {"source": "${localWorkspaceFolderBasename}-dist", "target": "/workspace/dist", "type": "volume"}, {"source": "${localWorkspaceFolderBasename}-backend-dist", "target": "/workspace/src/backend/base/dist", "type": "volume"}]}