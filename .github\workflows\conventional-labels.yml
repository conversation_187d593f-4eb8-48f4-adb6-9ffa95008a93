# Warning, do not check out untrusted code with
# the pull_request_target event.
name: Label PRs with Conventional Commits
on:
  pull_request_target:
    types: [opened, edited, synchronize]
  merge_group:

jobs:
  validate-pr:
    name: Validate PR
    runs-on: ubuntu-latest
    steps:
      - name: Validate the pull request
        id: validate
        uses: Namchee/conventional-pr@v0.15.6
        with:
          access_token: ${{ secrets.GITHUB_TOKEN }}
          issue: false

  label:
    needs: validate-pr
    name: Label PR
    runs-on: ubuntu-latest
    if: ${{ github.event.pull_request.user.type != 'Bot'}}
    steps:
      - uses: bcoe/conventional-release-labels@v1
        with:
          type_labels: '{"feat": "enhancement","fix": "bug","docs": "documentation","style": "style","refactor": "refactor","perf": "performance","test": "test","chore": "chore","build": "build"}'
