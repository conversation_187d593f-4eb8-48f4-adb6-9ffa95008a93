name: Test Docker images

on:
  push:
    branches: [main]
    paths:
      - "docker/**"
      - "uv.lock"
      - "pyproject.toml"
      - "src/backend/**"
      - ".github/workflows/docker_test.yml"
  pull_request:
    branches: [dev]
    paths:
      - "docker/**"
      - "uv.lock"
      - "pyproject.toml"
      - "src/**"
      - ".github/workflows/docker_test.yml"
  workflow_dispatch:

env:
  POETRY_VERSION: "1.8.2"

jobs:
  test-docker:
    runs-on: ubuntu-latest
    name: Test docker images
    steps:
      - uses: actions/checkout@v4
      - name: Build image
        run: |
          docker build -t langflowai/langflow:latest-dev \
            -f docker/build_and_push.Dockerfile \
            .
      - name: Test image
        run: |
          expected_version=$(cat pyproject.toml | grep version | head -n 1 | cut -d '"' -f 2)
          version=$(docker run --rm --entrypoint bash langflowai/langflow:latest-dev -c "python -c 'from langflow.utils.version import get_version_info; print(get_version_info()[\"version\"])'")
          if [ "$expected_version" != "$version" ]; then
              echo "Expected version: $expected_version"
              echo "Actual version: $version"
              exit 1
          fi

      - name: Build backend image
        run: |
          docker build -t langflowai/langflow-backend:latest-dev \
            --build-arg LANGFLOW_IMAGE=langflowai/langflow:latest-dev \
            -f docker/build_and_push_backend.Dockerfile \
            .
      - name: Test backend image
        run: |
          expected_version=$(cat pyproject.toml | grep version | head -n 1 | cut -d '"' -f 2)
          version=$(docker run --rm --entrypoint bash langflowai/langflow-backend:latest-dev -c "python -c 'from langflow.utils.version import get_version_info; print(get_version_info()[\"version\"])'")
          if [ "$expected_version" != "$version" ]; then
              echo "Expected version: $expected_version"
              echo "Actual version: $version"
              exit 1
          fi
      - name: Build frontend image
        run: |
          docker build -t langflowai/langflow-frontend:latest-dev \
            -f docker/frontend/build_and_push_frontend.Dockerfile \
            .
