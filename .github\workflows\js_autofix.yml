name: autofix.ci

on:
  pull_request:
    paths:
      - "src/frontend/**"

permissions:
  contents: read

env:
  NODE_VERSION: "21"
jobs:
  autofix:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        id: setup-node
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Cache Node.js dependencies
        uses: actions/cache@v4
        id: npm-cache
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('src/frontend/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install Node.js dependencies
        run: |
          cd src/frontend
          npm ci
        if: ${{ steps.setup-node.outputs.cache-hit != 'true' }}
      - name: Run Biome
        run: |
          cd src/frontend
          npm run format

      - uses: autofix-ci/action@635ffb0c9798bd160680f18fd73371e355b85f27
