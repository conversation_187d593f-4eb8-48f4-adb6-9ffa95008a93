name: <PERSON><PERSON>end

on:
  workflow_call:
  workflow_dispatch:
    inputs:
      branch:
        description: "(Optional) Branch to checkout"
        required: false
        type: string


env:
  NODE_VERSION: "21"

jobs:
  run-linters:
    name: Run Biome
    runs-on: ubuntu-latest
    permissions:
      contents: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch || github.ref }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        id: setup-node
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Cache Node.js dependencies
        uses: actions/cache@v4
        id: npm-cache
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('src/frontend/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install Node.js dependencies
        run: |
          cd src/frontend
          npm install
        if: ${{ steps.setup-node.outputs.cache-hit != 'true' }}

      - name: Run Biome
        run: |
          cd src/frontend
          npx @biomejs/biome check --changed
