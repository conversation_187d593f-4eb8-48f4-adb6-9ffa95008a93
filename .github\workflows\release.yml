name: Langflow Release
run-name: Langflow Release by @${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      release_package_base:
        description: "Release Langflow Base"
        required: true
        type: boolean
        default: false
      release_package_main:
        description: "Release Langflow"
        required: true
        type: boolean
        default: false
      build_docker_base:
        description: "Build Docker Image for Langflow Base"
        required: true
        type: boolean
        default: false
      build_docker_main:
        description: "Build Docker Image for Langflow"
        required: true
        type: boolean
        default: false
      build_docker_ep:
        description: "Build Docker Image for Langflow with Entrypoint"
        required: false
        type: boolean
        default: false
      pre_release:
        description: "Pre-release"
        required: false
        type: boolean
        default: false
      create_release:
        description: "Whether to create a gh release"
        required: false
        type: boolean
        default: true



jobs:
  ci:
    if: ${{ github.event.inputs.release_package_base == 'true' || github.event.inputs.release_package_main == 'true' }}
    name: CI
    uses: ./.github/workflows/ci.yml
    with:
      python-versions: "['3.10', '3.11', '3.12', '3.13']"
      frontend-tests-folder: "tests"
      release: true
    secrets: inherit


  build-base:
    name: Build Langflow Base
    needs: [ci]
    if: inputs.release_package_base == true
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.check-version.outputs.version }}
      skipped: ${{ steps.check-version.outputs.skipped }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Environment
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"
          python-version: "3.13"
          prune-cache: false
      - name: Install the project
        run: uv sync
      - name: Check for dependency incompatibility
        run: uv pip check
      - name: Check Version
        id: check-version
        run: |
          version=$(uv tree | grep 'langflow-base' | awk '{print $3}' | sed 's/^v//' | head -n 2 | xargs)
          last_released_version=$(curl -s "https://pypi.org/pypi/langflow-base/json" | jq -r '.releases | keys | .[]' | sort -V | tail -n 1)
          if [ "$version" = "$last_released_version" ]; then
            echo "Version $version is already released. Skipping release."
            echo skipped=true >> $GITHUB_OUTPUT
            exit 0
          else
            echo version=$version >> $GITHUB_OUTPUT
            echo skipped=false >> $GITHUB_OUTPUT
          fi
      - name: Build project for distribution
        if: steps.check-version.outputs.skipped == 'false'
        run: make build base=true args="--wheel"
      - name: Test CLI
        if: steps.check-version.outputs.skipped == 'false'
        run: |
          # TODO: Unsure why the whl is not built in src/backend/base/dist
          mkdir src/backend/base/dist
          mv dist/*.whl src/backend/base/dist
          uv pip install src/backend/base/dist/*.whl
          uv run python -m langflow run --host localhost --port 7860 --backend-only &
          SERVER_PID=$!
          # Wait for the server to start
          timeout 120 bash -c 'until curl -f http://localhost:7860/api/v1/auto_login; do sleep 2; done' || (echo "Server did not start in time" && kill $SERVER_PID && exit 1)
          # Terminate the server
          kill $SERVER_PID || (echo "Failed to terminate the server" && exit 1)
          sleep 20 # give the server some time to terminate
          # Check if the server is still running
          if kill -0 $SERVER_PID 2>/dev/null; then
            echo "Failed to terminate the server"
            exit 0
          else
            echo "Server terminated successfully"
          fi

      # PyPI publishing moved to after cross-platform testing

      - name: Upload Artifact
        if: steps.check-version.outputs.skipped == 'false'
        uses: actions/upload-artifact@v4
        with:
          name: dist-base
          path: src/backend/base/dist

  build-main:
    name: Build Langflow Main
    if: inputs.release_package_main == true
    needs: [build-base]
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.check-version.outputs.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Environment
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"
          python-version: "3.13"
          prune-cache: false
      - name: Install the project
        run: uv sync
      - name: Check for dependency incompatibility
        run: uv pip check

      # If pre-release is true, we need to check if  ["a", "b", "rc", "dev", "post"] is in the version string
      # if the version string is incorrect, we need to exit the workflow
      - name: Check if pre-release
        if: inputs.pre_release == 'true'
        run: |
          version=$(uv tree | grep 'langflow' | grep -v 'langflow-base' | awk '{print $2}' | sed 's/^v//')
          if [[ "${version}" =~ ^([0-9]+\.)?([0-9]+\.)?[0-9]+((a|b|rc|dev|post)([0-9]+))$ ]]; then
            echo "Pre-release version detected. Continuing with the release."
          else
            echo "Invalid pre-release version detected. Exiting the workflow."
            exit 1
          fi
      - name: Check Version
        id: check-version
        run: |
          version=$(uv tree | grep 'langflow' | grep -v 'langflow-base' | awk '{print $2}' | sed 's/^v//')
          last_released_version=$(curl -s "https://pypi.org/pypi/langflow/json" | jq -r '.releases | keys | .[]' | sort -V | tail -n 1)
          if [ "$version" = "$last_released_version" ]; then
            echo "Version $version is already released. Skipping release."
            exit 1
          else
            echo version=$version >> $GITHUB_OUTPUT
          fi
      - name: Wait for PyPI Propagation
        if: needs.build-base.outputs.skipped == 'false'
        run: sleep 300 # wait for 5 minutes to ensure PyPI propagation

      - name: Build project for distribution
        run: make build main=true args="--no-sources --wheel"
      - name: Test CLI
        run: |
          uv pip install dist/*.whl
          uv run python -m langflow run --host localhost --port 7860 --backend-only &
          SERVER_PID=$!
          # Wait for the server to start
          timeout 120 bash -c 'until curl -f http://localhost:7860/health_check; do sleep 2; done' || (echo "Server did not start in time" && kill $SERVER_PID && exit 1)
          # Terminate the server
          kill $SERVER_PID || (echo "Failed to terminate the server" && exit 1)
          sleep 20 # give the server some time to terminate
          # Check if the server is still running
          if kill -0 $SERVER_PID 2>/dev/null; then
            echo "Failed to terminate the server"
            exit 0
          else
            echo "Server terminated successfully"
          fi

      # PyPI publishing moved to after cross-platform testing

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: dist-main
          path: dist

  test-cross-platform:
    name: Test Cross-Platform Installation
    if: inputs.release_package_base == true || inputs.release_package_main == true
    needs: [build-base, build-main]
    uses: ./.github/workflows/cross-platform-test.yml
    with:
      base-artifact-name: "dist-base"
      main-artifact-name: "dist-main"

  publish-base:
    name: Publish Langflow Base to PyPI
    if: inputs.release_package_base == true
    needs: [build-base, test-cross-platform]
    runs-on: ubuntu-latest
    steps:
      - name: Download base artifact
        uses: actions/download-artifact@v4
        with:
          name: dist-base
          path: src/backend/base/dist
      - name: Setup Environment
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: false
          python-version: "3.13"
      - name: Publish base to PyPI
        if: needs.build-base.outputs.skipped == 'false'
        env:
          UV_PUBLISH_TOKEN: ${{ secrets.PYPI_API_TOKEN }}
        run: |
          cd src/backend/base && uv publish dist/*.whl

  publish-main:
    name: Publish Langflow Main to PyPI
    if: inputs.release_package_main == true
    needs: [build-main, test-cross-platform, publish-base]
    runs-on: ubuntu-latest
    steps:
      - name: Download main artifact
        uses: actions/download-artifact@v4
        with:
          name: dist-main
          path: dist
      - name: Setup Environment
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: false
          python-version: "3.13"
      - name: Publish main to PyPI
        env:
          UV_PUBLISH_TOKEN: ${{ secrets.PYPI_API_TOKEN }}
        run: |
          uv publish dist/*.whl

  call_docker_build_base:
    name: Call Docker Build Workflow for Langflow Base
    if: inputs.build_docker_base == true
    needs: [publish-base, publish-main]
    uses: ./.github/workflows/docker-build.yml
    with:
      base_version: ${{ needs.publish-base.needs.build-base.outputs.version }}
      main_version: ${{ needs.publish-main.needs.build-main.outputs.version }}
      release_type: base
      pre_release: ${{ inputs.pre_release }}
    secrets: inherit

  call_docker_build_main_ep:
    name: Call Docker Build Workflow for Langflow with Entrypoint
    if: inputs.build_docker_ep == true
    needs: [publish-main, call_docker_build_base]
    uses: ./.github/workflows/docker-build.yml
    with:
      main_version: ${{ needs.publish-main.needs.build-main.outputs.version }}
      release_type: main-ep
      pre_release: False
    secrets: inherit

  call_docker_build_main:
    name: Call Docker Build Workflow for Langflow
    if: inputs.build_docker_main == true
    needs: [publish-main, call_docker_build_main_ep]
    uses: ./.github/workflows/docker-build.yml
    with:
      main_version: ${{ needs.publish-main.needs.build-main.outputs.version }}
      release_type: main
      pre_release: ${{ inputs.pre_release }}
    secrets: inherit

  call_docker_build_main_all:
    name: Call Docker Build Workflow for langflow-all
    if: inputs.build_docker_main == true
    needs: [publish-main]
    uses: ./.github/workflows/docker-build.yml
    with:
      main_version: ${{ needs.publish-main.needs.build-main.outputs.version }}
      release_type: main-all
      pre_release: ${{ inputs.pre_release }}
    secrets: inherit


  create_release:
    name: Create Release
    runs-on: ubuntu-latest
    needs: publish-main
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: dist-main
          path: dist
      - name: Create Release
        uses: ncipollo/release-action@v1
        with:
          artifacts: "dist/*"
          token: ${{ secrets.GITHUB_TOKEN }}
          draft: false
          generateReleaseNotes: true
          prerelease: ${{ inputs.pre_release }}
          tag: ${{ needs.publish-main.needs.build-main.outputs.version }}
          commit: ${{ github.ref }}
