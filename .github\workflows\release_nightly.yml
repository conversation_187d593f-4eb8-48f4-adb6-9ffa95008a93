name: Langflow Nightly Build
run-name: Langflow Nightly Release by @${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      build_docker_base:
        description: "Build Docker Image for Langflow Nightly Base"
        required: true
        type: boolean
        default: false
      build_docker_main:
        description: "Build Docker Image for Langflow Nightly"
        required: true
        type: boolean
        default: false
      build_docker_ep:
        description: "Build Docker Image for Langflow Nightly with Entrypoint"
        required: false
        type: boolean
        default: false
      nightly_tag_main:
        description: "Tag for the nightly main build"
        required: true
        type: string
      nightly_tag_base:
        description: "Tag for the nightly base build"
        required: true
        type: string
  workflow_call:
    inputs:
      build_docker_base:
        description: "Build Docker Image for Langflow Nightly Base"
        required: true
        type: boolean
        default: false
      build_docker_main:
        description: "Build Docker Image for Langflow Nightly"
        required: true
        type: boolean
        default: false
      build_docker_ep:
        description: "Build Docker Image for Langflow Nightly with Entrypoint"
        required: false
        type: boolean
        default: false
      nightly_tag_main:
        description: "Tag for the nightly main build"
        required: true
        type: string
      nightly_tag_base:
        description: "Tag for the nightly base build"
        required: true
        type: string

env:
  POETRY_VERSION: "1.8.3"
  PYTHON_VERSION: "3.13"

jobs:
  build-nightly-base:
    name: Build Langflow Nightly Base
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    outputs:
      version: ${{ steps.verify.outputs.version }}
      skipped: ${{ steps.verify.outputs.skipped }}
    steps:
      - name: Check out the code at a specific ref
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.nightly_tag_main }}
          persist-credentials: true
      - name: "Setup Environment"
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"
          python-version: ${{ env.PYTHON_VERSION }}
          prune-cache: false
      - name: Install the project
        run: uv sync

      - name: Verify Nightly Name and Version
        id: verify
        run: |
          name=$(uv tree | grep 'langflow-base' | awk '{print $2}' | head -n 1)
          version=$(uv tree | grep 'langflow-base' | awk '{print $3}' | head -n 1)
          if [ "$name" != "langflow-base-nightly" ]; then
            echo "Name $name does not match langflow-base-nightly. Exiting the workflow."
            exit 1
          fi
          if [ "$version" != "${{ inputs.nightly_tag_base }}" ]; then
            echo "Version $version does not match nightly tag ${{ inputs.nightly_tag_base }}. Exiting the workflow."
            exit 1
          fi
          # Strip the leading `v` from the version
          version=$(echo $version | sed 's/^v//')
          echo "version=$version" >> $GITHUB_OUTPUT

      - name: Build project for distribution
        run: |
          rm -rf src/backend/base/dist
          rm -rf dist
          make build base=true args="--wheel"

      - name: Test CLI
        run: |
          # TODO: Unsure why the whl is not built in src/backend/base/dist
          mkdir src/backend/base/dist
          mv dist/*.whl src/backend/base/dist/
          uv pip install src/backend/base/dist/*.whl
          uv run python -m langflow run --host localhost --port 7860 --backend-only &
          SERVER_PID=$!
          # Wait for the server to start
          timeout 120 bash -c 'until curl -f http://localhost:7860/api/v1/auto_login; do sleep 2; done' || (echo "Server did not start in time" && kill $SERVER_PID && exit 1)
          # Terminate the server
          kill $SERVER_PID || (echo "Failed to terminate the server" && exit 1)
          sleep 20 # give the server some time to terminate
          # Check if the server is still running
          if kill -0 $SERVER_PID 2>/dev/null; then
            echo "Failed to terminate the server"
            exit 0
          else
            echo "Server terminated successfully"
          fi

      # PyPI publishing moved to after cross-platform testing

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: dist-nightly-base
          path: src/backend/base/dist

  build-nightly-main:
    name: Build Langflow Nightly Main
    needs: [build-nightly-base]
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.verify.outputs.version }}
    defaults:
      run:
        shell: bash
    steps:
      - name: Check out the code at a specific ref
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.nightly_tag_main}}
          persist-credentials: true
      - name: "Setup Environment"
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"
          python-version: ${{ env.PYTHON_VERSION }}
          prune-cache: false
      - name: Install the project
        run: uv sync

      - name: Verify Nightly Name and Version
        id: verify
        run: |
          name=$(uv tree | grep 'langflow' | grep -v 'langflow-base' | awk '{print $1}')
          version=$(uv tree | grep 'langflow' | grep -v 'langflow-base' | awk '{print $2}')
          if [ "$name" != "langflow-nightly" ]; then
            echo "Name $name does not match langflow-nightly. Exiting the workflow."
            exit 1
          fi
          if [ "$version" != "${{ inputs.nightly_tag_main }}" ]; then
            echo "Version $version does not match nightly tag ${{ inputs.nightly_tag_main }}. Exiting the workflow."
            exit 1
          fi
          # Strip the leading `v` from the version
          version=$(echo $version | sed 's/^v//')
          echo "version=$version" >> $GITHUB_OUTPUT
      - name: Wait for PyPI Propagation
        if: needs.build-nightly-base.outputs.skipped == 'false'
        run: sleep 300 # wait for 5 minutes to ensure PyPI propagation of base

      - name: Build project for distribution
        run: make build main=true args="--no-sources --wheel"
      - name: Test CLI
        run: |
          uv pip install dist/*.whl
          uv run python -m langflow run --host localhost --port 7860 --backend-only &
          SERVER_PID=$!
          # Wait for the server to start
          timeout 120 bash -c 'until curl -f http://localhost:7860/health_check; do sleep 2; done' || (echo "Server did not start in time" && kill $SERVER_PID && exit 1)
          # Terminate the server
          kill $SERVER_PID || (echo "Failed to terminate the server" && exit 1)
          sleep 20 # give the server some time to terminate
          # Check if the server is still running
          if kill -0 $SERVER_PID 2>/dev/null; then
            echo "Failed to terminate the server"
            exit 0
          else
            echo "Server terminated successfully"
          fi

      # PyPI publishing moved to after cross-platform testing

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: dist-nightly-main
          path: dist

  test-cross-platform:
    name: Test Cross-Platform Installation
    needs: [build-nightly-base, build-nightly-main]
    uses: ./.github/workflows/cross-platform-test.yml
    with:
      base-artifact-name: "dist-nightly-base"
      main-artifact-name: "dist-nightly-main"

  publish-nightly-base:
    name: Publish Langflow Base Nightly to PyPI
    needs: [build-nightly-base, test-cross-platform]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Download base artifact
        uses: actions/download-artifact@v4
        with:
          name: dist-nightly-base
          path: src/backend/base/dist
      - name: Setup Environment
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: false
          python-version: "3.13"
      - name: Publish base to PyPI
        if: needs.build-nightly-base.outputs.skipped == 'false'
        env:
          POETRY_PYPI_TOKEN_PYPI: ${{ secrets.PYPI_API_TOKEN }}
          UV_PUBLISH_TOKEN: ${{ secrets.PYPI_API_TOKEN }}
        run: |
          make publish base=true

  publish-nightly-main:
    name: Publish Langflow Main Nightly to PyPI
    needs: [build-nightly-main, test-cross-platform, publish-nightly-base]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Download main artifact
        uses: actions/download-artifact@v4
        with:
          name: dist-nightly-main
          path: dist
      - name: Setup Environment
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: false
          python-version: "3.13"
      - name: Publish to PyPI
        env:
          POETRY_PYPI_TOKEN_PYPI: ${{ secrets.PYPI_API_TOKEN }}
          UV_PUBLISH_TOKEN: ${{ secrets.PYPI_API_TOKEN }}
        run: |
          make publish main=true

  call_docker_build_base:
    name: Call Docker Build Workflow for Langflow Base
    if: always() && ${{ inputs.build_docker_base == 'true' }}
    needs: [build-nightly-base, build-nightly-main]
    uses: ./.github/workflows/docker-build.yml
    with:
      release_type: nightly-base
      base_version: ${{ inputs.nightly_tag_base }}
      main_version: ${{ inputs.nightly_tag_main }}
    secrets: inherit

  call_docker_build_main:
    name: Call Docker Build Workflow for Langflow
    if: always() && ${{ inputs.build_docker_main == 'true' }}
    needs: [build-nightly-main, call_docker_build_base]
    uses: ./.github/workflows/docker-build.yml
    with:
      release_type: nightly-main
      main_version: ${{ inputs.nightly_tag_main }}
    secrets: inherit

  # TODO: Uncomment this when our runner can fit the builds that contain pytorch (and other large dependencies)
  # call_docker_build_main_all:
  #   name: Call Docker Build Workflow for langflow-all
  #   if: always() && ${{ inputs.build_docker_main == 'true' }}
  #   needs: [build-nightly-main]
  #   uses: ./.github/workflows/docker-build.yml
  #   with:
  #     release_type: nightly-main-all
  #     main_version: ${{ inputs.nightly_tag_main }}
  #   secrets: inherit

  call_docker_build_main_ep:
    name: Call Docker Build Workflow for Langflow with Entrypoint
    if: always() && ${{ inputs.build_docker_ep == 'true' }}
    needs: [build-nightly-main, call_docker_build_main]
    uses: ./.github/workflows/docker-build.yml
    with:
      release_type: main-ep
      main_version: ${{ inputs.nightly_tag_main }}
    secrets: inherit
