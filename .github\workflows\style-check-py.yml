name: <PERSON>uff Style Check

on:
  pull_request:
    types: [opened, synchronize, reopened, auto_merge_enabled]
    paths:
      - "**/*.py"

jobs:
  lint:
    name: Ruff Style Check
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version:
          - "3.13"
    steps:
      - name: Check out the code at a specific ref
        uses: actions/checkout@v4
      - name: "Setup Environment"
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"
          python-version: ${{ matrix.python-version }}
          prune-cache: false
      - name: Register problem matcher
        run: echo "::add-matcher::.github/workflows/matchers/ruff.json"
      - name: Run Ruff Check
        run: uv run --only-dev ruff check --output-format=github .

