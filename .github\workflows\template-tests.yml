name: Template Tests

on:
  pull_request:
    paths:
      - 'src/backend/base/langflow/initial_setup/starter_projects/**'
      - 'src/backend/tests/unit/template/test_starter_projects.py'
      - 'src/backend/base/langflow/utils/template_validation.py'
      - '.github/workflows/template-tests.yml'
  workflow_dispatch:

permissions:
  contents: read
  pull-requests: read

jobs:
  test-starter-projects:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python 3.12
      uses: actions/setup-python@v5
      with:
        python-version: 3.12

    - name: Install uv
      uses: astral-sh/setup-uv@v6
      with:
        version: "latest"

    - name: Install dependencies
      run: |
        uv sync --dev

    - name: Test all starter project templates
      run: |
        uv run pytest src/backend/tests/unit/template/test_starter_projects.py -v -n auto