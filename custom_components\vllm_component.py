"""
自定义vLLM组件
独立的vLLM模型组件，不依赖核心代码修改
"""

from typing import Any
from langchain_openai import ChatOpenAI
from pydantic.v1 import SecretStr

from langflow.custom import Component
from langflow.io import DropdownInput, MessageInput, MultilineInput, SecretStrInput, SliderInput, StrInput, BoolInput
from langflow.schema import Message
from langflow.template.field.base import Output

# vLLM常用模型列表
VLLM_MODELS = [
    "Qwen/Qwen2.5-7B-Instruct",
    "Qwen/Qwen2.5-14B-Instruct", 
    "Qwen/Qwen2.5-32B-Instruct",
    "Qwen/Qwen2.5-72B-Instruct",
    "meta-llama/Llama-3.1-8B-Instruct",
    "meta-llama/Llama-3.1-70B-Instruct",
    "microsoft/Phi-3-mini-4k-instruct",
    "mistralai/Mistral-7B-Instruct-v0.3",
    "deepseek-ai/DeepSeek-V2.5",
    "01-ai/Yi-34B-Chat",
    "custom-model"  # 用户自定义模型
]


class VLLMComponent(Component):
    display_name = "vLLM Local Model"
    description = "连接本地部署的vLLM服务，支持OpenAI兼容API"
    icon = "🖥️"
    category = "models"

    inputs = [
        StrInput(
            name="api_base",
            display_name="vLLM API地址",
            value="http://*************:8888/v1",
            info="vLLM服务的API地址",
        ),
        DropdownInput(
            name="model_name",
            display_name="模型名称",
            options=["Qwen3-32B-AWQ"] + VLLM_MODELS,
            value="Qwen3-32B-AWQ",
            info="选择或输入vLLM中加载的模型名称",
        ),
        SecretStrInput(
            name="api_key",
            display_name="API密钥",
            value="sk-local-vllm-key",
            info="vLLM服务的API密钥，如果未设置可以使用任意字符串",
            required=False,
        ),
        MessageInput(
            name="input_message",
            display_name="输入消息",
            info="发送给模型的输入消息",
        ),
        MultilineInput(
            name="system_message",
            display_name="系统消息",
            info="设置助手行为的系统消息",
            value="You are a helpful assistant.",
        ),
        SliderInput(
            name="temperature",
            display_name="Temperature",
            value=0.7,
            range_spec=(0.0, 1.0, 0.01),
            info="控制输出的随机性，0.0-1.0",
        ),
        SliderInput(
            name="max_tokens",
            display_name="最大Token数",
            value=40960,
            range_spec=(1, 50000, 1),
            info="生成文本的最大长度",
        ),
        SliderInput(
            name="top_p",
            display_name="Top P",
            value=1.0,
            range_spec=(0.0, 1.0, 0.01),
            info="核采样参数",
            advanced=True,
        ),
        SliderInput(
            name="frequency_penalty",
            display_name="频率惩罚",
            value=0.0,
            range_spec=(-2.0, 2.0, 0.01),
            info="降低重复内容的频率惩罚",
            advanced=True,
        ),
        SliderInput(
            name="presence_penalty",
            display_name="存在惩罚",
            value=0.0,
            range_spec=(-2.0, 2.0, 0.01),
            info="鼓励谈论新话题的存在惩罚",
            advanced=True,
        ),
        BoolInput(
            name="stream",
            display_name="流式输出",
            value=False,
            info="是否启用流式响应",
            advanced=True,
        ),
    ]

    outputs = [
        Output(display_name="响应", name="response", method="generate_response"),
    ]

    def generate_response(self) -> Message:
        """生成模型响应"""
        try:
            # 创建ChatOpenAI实例连接vLLM
            llm = ChatOpenAI(
                base_url=self.api_base,
                api_key=SecretStr(self.api_key or "sk-local-vllm-key").get_secret_value(),
                model=self.model_name,
                temperature=self.temperature,
                max_tokens=self.max_tokens or None,
                top_p=self.top_p,
                frequency_penalty=self.frequency_penalty,
                presence_penalty=self.presence_penalty,
                streaming=self.stream,
            )
            
            # 准备消息
            messages = []
            if self.system_message:
                messages.append(("system", self.system_message))
            
            # 处理输入消息
            if isinstance(self.input_message, Message):
                messages.append(("human", self.input_message.text))
            else:
                messages.append(("human", str(self.input_message)))
            
            # 调用模型
            response = llm.invoke(messages)
            
            # 返回响应
            return Message(text=response.content)
            
        except Exception as e:
            error_msg = f"vLLM调用失败: {str(e)}"
            self.status = error_msg
            return Message(text=f"错误: {error_msg}")

    def build_config(self):
        """构建组件配置"""
        return {
            "api_base": {
                "display_name": "vLLM API地址",
                "info": "vLLM服务的API地址"
            },
            "model_name": {
                "display_name": "模型名称", 
                "options": VLLM_MODELS
            },
            "api_key": {
                "display_name": "API密钥",
                "password": True
            },
            "input_message": {
                "display_name": "输入消息"
            },
            "system_message": {
                "display_name": "系统消息",
                "multiline": True
            },
            "temperature": {
                "display_name": "Temperature",
                "field_type": "float",
                "range_spec": (0.0, 1.0, 0.01)
            },
            "max_tokens": {
                "display_name": "最大Token数",
                "field_type": "int"
            }
        }
