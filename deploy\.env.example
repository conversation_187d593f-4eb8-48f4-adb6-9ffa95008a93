DOMAIN=localhost
STACK_NAME=langflow-stack

TRAEFIK_PUBLIC_NETWORK=traefik-public
TRAEFIK_TAG=langflow-traefik
TRAEFIK_PUBLIC_TAG=traefik-public


# Langflow backend configuration
LANGFLOW_LOG_LEVEL=debug
LANGFLOW_SUPERUSER=superuser
LANGFLOW_SUPERUSER_PASSWORD=superuser
LANGFLOW_NEW_USER_IS_ACTIVE=False


# Langflow frontend configuration
BACKEND_URL=http://backend:7860

# RabbitMQ configuration
RABBITMQ_DEFAULT_USER=langflow
RABBITMQ_DEFAULT_PASS=langflow

# Database configuration
DB_USER=langflow
DB_PASSWORD=langflow
DB_HOST=db
DB_PORT=5432
DB_NAME=langflow

# DB configuration
POSTGRES_USER=langflow
POSTGRES_PASSWORD=langflow
POSTGRES_DB=langflow
POSTGRES_PORT=5432

# Flower configuration
# Disable until https://github.com/langflow-ai/langflow/pull/2655 gets released
#LANGFLOW_CACHE_TYPE=redis
LANGFLOW_REDIS_HOST=result_backend
LANGFLOW_REDIS_PORT=6379
LANGFLOW_REDIS_DB=0
LANGFLOW_REDIS_EXPIRE=3600
LANGFLOW_REDIS_PASSWORD=
FLOWER_UNAUTHENTICATED_API=True
BROKER_URL=************************************
RESULT_BACKEND=redis://result_backend:6379/0
C_FORCE_ROOT="true"


# PGAdmin configuration
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=admin
