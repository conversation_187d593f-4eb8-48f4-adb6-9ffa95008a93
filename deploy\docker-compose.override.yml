services:
  proxy:
    ports:
      - "80:80"
      - "8090:8080"
    command:
      # Enable Docker in Traefik, so that it reads labels from Docker services
      - --providers.docker
      # Add a constraint to only use services with the label for this stack
      # from the env var TRAEFIK_TAG
      - --providers.docker.constraints=Label(`traefik.constraint-label-stack`, `${TRAEFIK_TAG?Variable not set}`)
      # Do not expose all Docker services, only the ones explicitly exposed
      - --providers.docker.exposedbydefault=false
      # Disable Docker Swarm mode for local development
      # - --providers.docker.swarmmode
      # Enable the access log, with HTTP requests
      - --accesslog
      # Enable the Traefik log, for configurations and errors
      - --log
      # Enable the Dashboard and API
      - --api
      # Enable the Dashboard and API in insecure mode for local development
      - --api.insecure=true
    labels:
      - traefik.enable=true
      - traefik.http.routers.${STACK_NAME?Variable not set}-traefik-public-http.rule=Host(`${DOMAIN?Variable not set}`)
      - traefik.http.services.${STACK_NAME?Variable not set}-traefik-public.loadbalancer.server.port=80

  result_backend:
    ports:
      - "6379:6379"

  pgadmin:
    ports:
      - "5050:5050"

  flower:
    ports:
      - "5555:5555"

  backend:
    labels:
      - traefik.enable=true
      - traefik.constraint-label-stack=${TRAEFIK_TAG?Variable not set}
      - traefik.http.routers.${STACK_NAME?Variable not set}-backend-http.rule=PathPrefix(`/api/v1`) || PathPrefix(`/api/v2`) || PathPrefix(`/docs`) || PathPrefix(`/health`)
      - traefik.http.services.${STACK_NAME?Variable not set}-backend.loadbalancer.server.port=7860

  frontend:
    labels:
      - traefik.enable=true
      - traefik.constraint-label-stack=${TRAEFIK_TAG?Variable not set}
      - traefik.http.routers.${STACK_NAME?Variable not set}-frontend-http.rule=PathPrefix(`/`)
      - traefik.http.services.${STACK_NAME?Variable not set}-frontend.loadbalancer.server.port=80

  celeryworker:
    labels:
      - traefik.enable=true
      - traefik.constraint-label-stack=${TRAEFIK_TAG?Variable not set}
      - traefik.http.routers.${STACK_NAME?Variable not set}-celeryworker-http.rule=PathPrefix(`/api/v1`) || PathPrefix(`/api/v2`) || PathPrefix(`/docs`) || PathPrefix(`/health`)
      - traefik.http.services.${STACK_NAME?Variable not set}-celeryworker.loadbalancer.server.port=7860

networks:
  traefik-public:
    # For local dev, don't expect an external Traefik network
    external: false
